{"name": "atma-load-testing", "version": "1.0.0", "description": "Simple load testing for ATMA ecosystem user flow", "main": "test-user-flow.js", "scripts": {"test": "node test-user-flow.js", "test:250": "node test-user-flow.js 250", "test:50": "node test-user-flow.js 50", "test:10": "node test-user-flow.js 10"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}, "devDependencies": {}, "keywords": ["testing", "load-test", "atma"], "author": "ATMA Team", "license": "ISC"}