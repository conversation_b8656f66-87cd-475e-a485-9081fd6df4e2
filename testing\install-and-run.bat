@echo off
echo ========================================
echo ATMA Load Testing - Quick Start
echo ========================================
echo.

echo Installing dependencies...
npm install

echo.
echo ========================================
echo Available test options:
echo ========================================
echo 1. Test with 10 users (quick test)
echo 2. Test with 50 users (medium test)  
echo 3. Test with 250 users (full load test)
echo 4. Custom number of users
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo.
    echo Running test with 10 users...
    node test-user-flow.js 10
) else if "%choice%"=="2" (
    echo.
    echo Running test with 50 users...
    node test-user-flow.js 50
) else if "%choice%"=="3" (
    echo.
    echo Running test with 250 users...
    node test-user-flow.js 250
) else if "%choice%"=="4" (
    set /p custom="Enter number of users: "
    echo.
    echo Running test with %custom% users...
    node test-user-flow.js %custom%
) else (
    echo Invalid choice. Running default test with 10 users...
    node test-user-flow.js 10
)

echo.
echo ========================================
echo Test completed!
echo ========================================
pause
