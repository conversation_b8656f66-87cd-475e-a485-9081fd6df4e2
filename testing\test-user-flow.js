const axios = require('axios');
const WebSocket = require('ws');

// Configuration
const CONFIG = {
  API_GATEWAY: 'http://localhost:3000',
  NOTIFICATION_WS: 'ws://localhost:3005',
  CONCURRENT_USERS: parseInt(process.argv[2]) || 250,
  DELAY_BETWEEN_BATCHES: 100, // ms
  BATCH_SIZE: 10
};

// Sample assessment data
const SAMPLE_ASSESSMENT = {
  riasec: {
    realistic: Math.floor(Math.random() * 100),
    investigative: Math.floor(Math.random() * 100),
    artistic: Math.floor(Math.random() * 100),
    social: Math.floor(Math.random() * 100),
    enterprising: Math.floor(Math.random() * 100),
    conventional: Math.floor(Math.random() * 100)
  },
  ocean: {
    conscientiousness: Math.floor(Math.random() * 100),
    extraversion: Math.floor(Math.random() * 100),
    agreeableness: Math.floor(Math.random() * 100),
    neuroticism: Math.floor(Math.random() * 100),
    openness: Math.floor(Math.random() * 100)
  },
  viaIs: {
    creativity: Math.floor(Math.random() * 100),
    curiosity: Math.floor(Math.random() * 100),
    judgment: Math.floor(Math.random() * 100),
    loveOfLearning: Math.floor(Math.random() * 100),
    perspective: Math.floor(Math.random() * 100)
  }
};

// Statistics
const stats = {
  total: 0,
  registered: 0,
  loggedIn: 0,
  assessmentSubmitted: 0,
  notificationReceived: 0,
  dataRetrieved: 0,
  errors: 0,
  startTime: null,
  endTime: null
};

// User flow simulation
async function simulateUserFlow(userId) {
  const userEmail = `testuser${userId}@example.com`;
  const userPassword = 'password123';
  let userToken = null;
  let jobId = null;
  let resultId = null;

  try {
    console.log(`[User ${userId}] Starting user flow...`);

    // Step 1: Register
    try {
      const registerResponse = await axios.post(`${CONFIG.API_GATEWAY}/api/auth/register`, {
        email: userEmail,
        password: userPassword
      });
      
      if (registerResponse.data.success) {
        stats.registered++;
        console.log(`[User ${userId}] ✅ Registered successfully`);
      }
    } catch (error) {
      if (error.response?.status === 409) {
        console.log(`[User ${userId}] ⚠️ User already exists, continuing...`);
      } else {
        throw error;
      }
    }

    // Step 2: Login
    const loginResponse = await axios.post(`${CONFIG.API_GATEWAY}/api/auth/login`, {
      email: userEmail,
      password: userPassword
    });

    if (loginResponse.data.success && loginResponse.data.data.token) {
      userToken = loginResponse.data.data.token;
      stats.loggedIn++;
      console.log(`[User ${userId}] ✅ Logged in successfully`);
    } else {
      throw new Error('Login failed');
    }

    // Step 3: Submit Assessment
    const assessmentResponse = await axios.post(
      `${CONFIG.API_GATEWAY}/api/assessment/submit`,
      SAMPLE_ASSESSMENT,
      {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (assessmentResponse.data.success && assessmentResponse.data.data.jobId) {
      jobId = assessmentResponse.data.data.jobId;
      stats.assessmentSubmitted++;
      console.log(`[User ${userId}] ✅ Assessment submitted, jobId: ${jobId}`);
    } else {
      throw new Error('Assessment submission failed');
    }

    // Step 4: Connect to WebSocket for notifications
    const ws = new WebSocket(`${CONFIG.NOTIFICATION_WS}?token=${userToken}`);
    
    const notificationPromise = new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('Notification timeout'));
      }, 30000); // 30 second timeout

      ws.on('open', () => {
        console.log(`[User ${userId}] 🔌 Connected to notification service`);
      });

      ws.on('message', (data) => {
        try {
          const notification = JSON.parse(data);
          if (notification.type === 'analysis-complete' && notification.data.jobId === jobId) {
            resultId = notification.data.resultId;
            stats.notificationReceived++;
            console.log(`[User ${userId}] 🔔 Notification received, resultId: ${resultId}`);
            clearTimeout(timeout);
            ws.close();
            resolve(resultId);
          }
        } catch (e) {
          console.log(`[User ${userId}] ⚠️ Invalid notification format`);
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });

    // Wait for notification or timeout
    try {
      resultId = await notificationPromise;
    } catch (error) {
      console.log(`[User ${userId}] ⚠️ Notification timeout, checking job status manually...`);
      
      // Fallback: Check job status manually
      for (let i = 0; i < 10; i++) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        try {
          const jobResponse = await axios.get(
            `${CONFIG.API_GATEWAY}/api/archive/jobs/${jobId}`,
            {
              headers: { 'Authorization': `Bearer ${userToken}` }
            }
          );
          
          if (jobResponse.data.success && jobResponse.data.data.status === 'completed') {
            resultId = jobResponse.data.data.resultId;
            console.log(`[User ${userId}] ✅ Job completed manually, resultId: ${resultId}`);
            break;
          }
        } catch (e) {
          // Continue checking
        }
      }
    }

    // Step 5: Retrieve data from archive
    if (resultId) {
      const archiveResponse = await axios.get(
        `${CONFIG.API_GATEWAY}/api/archive/results/${resultId}`,
        {
          headers: { 'Authorization': `Bearer ${userToken}` }
        }
      );

      if (archiveResponse.data.success) {
        stats.dataRetrieved++;
        console.log(`[User ${userId}] ✅ Data retrieved successfully`);
      }
    }

    console.log(`[User ${userId}] 🎉 User flow completed successfully!`);

  } catch (error) {
    stats.errors++;
    console.error(`[User ${userId}] ❌ Error: ${error.message}`);
    if (error.response) {
      console.error(`[User ${userId}] Response status: ${error.response.status}`);
      console.error(`[User ${userId}] Response data:`, error.response.data);
    }
  }
}

// Main execution
async function runLoadTest() {
  console.log(`🚀 Starting load test with ${CONFIG.CONCURRENT_USERS} concurrent users`);
  console.log(`📊 Batch size: ${CONFIG.BATCH_SIZE}, Delay: ${CONFIG.DELAY_BETWEEN_BATCHES}ms`);
  console.log('='.repeat(60));

  stats.total = CONFIG.CONCURRENT_USERS;
  stats.startTime = new Date();

  const promises = [];
  
  // Create users in batches to avoid overwhelming the system
  for (let i = 0; i < CONFIG.CONCURRENT_USERS; i += CONFIG.BATCH_SIZE) {
    const batchPromises = [];
    
    for (let j = i; j < Math.min(i + CONFIG.BATCH_SIZE, CONFIG.CONCURRENT_USERS); j++) {
      batchPromises.push(simulateUserFlow(j + 1));
    }
    
    promises.push(...batchPromises);
    
    // Wait between batches
    if (i + CONFIG.BATCH_SIZE < CONFIG.CONCURRENT_USERS) {
      await new Promise(resolve => setTimeout(resolve, CONFIG.DELAY_BETWEEN_BATCHES));
    }
  }

  // Wait for all users to complete
  await Promise.allSettled(promises);
  
  stats.endTime = new Date();
  const duration = (stats.endTime - stats.startTime) / 1000;

  // Print results
  console.log('\n' + '='.repeat(60));
  console.log('📈 LOAD TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`Total Users: ${stats.total}`);
  console.log(`✅ Registered: ${stats.registered} (${(stats.registered/stats.total*100).toFixed(1)}%)`);
  console.log(`🔑 Logged In: ${stats.loggedIn} (${(stats.loggedIn/stats.total*100).toFixed(1)}%)`);
  console.log(`📝 Assessment Submitted: ${stats.assessmentSubmitted} (${(stats.assessmentSubmitted/stats.total*100).toFixed(1)}%)`);
  console.log(`🔔 Notification Received: ${stats.notificationReceived} (${(stats.notificationReceived/stats.total*100).toFixed(1)}%)`);
  console.log(`📊 Data Retrieved: ${stats.dataRetrieved} (${(stats.dataRetrieved/stats.total*100).toFixed(1)}%)`);
  console.log(`❌ Errors: ${stats.errors} (${(stats.errors/stats.total*100).toFixed(1)}%)`);
  console.log(`⏱️ Duration: ${duration.toFixed(2)} seconds`);
  console.log(`🚀 Throughput: ${(stats.total/duration).toFixed(2)} users/second`);
  console.log('='.repeat(60));
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⏹️ Test interrupted by user');
  process.exit(0);
});

// Start the test
runLoadTest().catch(console.error);
